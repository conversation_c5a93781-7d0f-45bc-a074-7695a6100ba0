#!/usr/bin/env python3
"""
SiliconFlow Provider注册脚本
将自定义SiliconFlow Provider注册到R2R系统中
"""

import sys
import os
import importlib.util
from pathlib import Path

def register_siliconflow_provider():
    """
    注册SiliconFlow Provider到R2R系统
    这个函数需要在R2R启动前调用
    """
    
    # 获取当前脚本目录
    current_dir = Path(__file__).parent
    provider_file = current_dir / "siliconflow_embedding_provider_enhanced.py"
    
    if not provider_file.exists():
        raise FileNotFoundError(f"SiliconFlow provider file not found: {provider_file}")
    
    # 动态导入SiliconFlow provider
    spec = importlib.util.spec_from_file_location(
        "siliconflow_provider", 
        provider_file
    )
    siliconflow_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(siliconflow_module)
    
    # 获取provider类
    SiliconFlowEmbeddingProvider = siliconflow_module.SiliconFlowEmbeddingProvider
    SiliconFlowEmbeddingConfig = siliconflow_module.SiliconFlowEmbeddingConfig
    
    # 注册到R2R系统
    try:
        # 导入R2R相关模块
        from py.core.main.assembly.factory import R2RProviderFactory
        from py.core.main.abstractions import R2RProviders
        from py.core.providers.embeddings import __all__ as embedding_providers
        
        # 扩展factory的create_embedding_provider方法
        original_create_embedding_provider = R2RProviderFactory.create_embedding_provider
        
        @staticmethod
        def enhanced_create_embedding_provider(embedding, *args, **kwargs):
            if embedding.provider == "siliconflow":
                return SiliconFlowEmbeddingProvider(embedding)
            else:
                return original_create_embedding_provider(embedding, *args, **kwargs)
        
        # 替换原方法
        R2RProviderFactory.create_embedding_provider = enhanced_create_embedding_provider
        
        # 更新支持的provider列表
        if "SiliconFlowEmbeddingProvider" not in embedding_providers:
            embedding_providers.append("SiliconFlowEmbeddingProvider")
        
        print("✅ SiliconFlow Provider注册成功")
        return True
        
    except ImportError as e:
        print(f"❌ 注册失败: R2R模块导入错误 - {e}")
        return False
    except Exception as e:
        print(f"❌ 注册失败: {e}")
        return False

def setup_environment():
    """设置环境变量和路径"""
    
    # 添加当前目录到Python路径
    current_dir = str(Path(__file__).parent)
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)
    
    # 设置R2R相关环境变量
    os.environ.setdefault('R2R_PROJECT_NAME', 'siliconflow_r2r')
    
    # 确保API key已设置
    api_key = os.getenv('SILICONFLOW_API_KEY') or os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("⚠️  警告: 未设置SILICONFLOW_API_KEY或OPENAI_API_KEY环境变量")
        return False
    
    print(f"✅ 环境设置完成，API Key: {api_key[:10]}...")
    return True

if __name__ == "__main__":
    print("🚀 开始注册SiliconFlow Provider...")
    
    if not setup_environment():
        print("❌ 环境设置失败")
        sys.exit(1)
    
    if register_siliconflow_provider():
        print("🎉 SiliconFlow Provider注册完成！")
        print("\n📋 使用说明:")
        print("1. 在配置文件中设置 provider = 'siliconflow'")
        print("2. 确保设置了正确的base_model和rerank_model")
        print("3. 启动R2R服务")
    else:
        print("💥 注册失败，请检查错误信息")
        sys.exit(1)
