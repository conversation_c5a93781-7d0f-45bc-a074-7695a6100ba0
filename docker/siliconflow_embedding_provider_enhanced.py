#!/usr/bin/env python3
"""
增强版SiliconFlow Embedding Provider
支持完整的embedding和reranking功能
"""

import asyncio
import json
import logging
from copy import copy
from typing import Any, Dict, List, Optional

import aiohttp
import requests
from pydantic import Field

from py.core.base.providers.embedding import EmbeddingConfig, EmbeddingProvider
from py.core.base.api.models import ChunkSearchResult

logger = logging.getLogger(__name__)


class SiliconFlowEmbeddingConfig(EmbeddingConfig):
    """SiliconFlow embedding provider configuration"""
    
    provider: str = "siliconflow"
    api_key: Optional[str] = Field(default=None, description="SiliconFlow API key")
    api_base: str = Field(default="https://api.siliconflow.cn/v1", description="SiliconFlow API base URL")
    rerank_model: Optional[str] = Field(default=None, description="Reranking model name")
    supports_reranking: bool = Field(default=True, description="Whether reranking is supported")


class SiliconFlowEmbeddingProvider(EmbeddingProvider):
    """
    SiliconFlow embedding provider with native reranking support
    """

    def __init__(self, config: SiliconFlowEmbeddingConfig):
        super().__init__(config)
        self.config: SiliconFlowEmbeddingConfig = config
        
        if not self.config.api_key:
            raise ValueError("SiliconFlow API key is required")
        
        self.headers = {
            "Authorization": f"Bearer {self.config.api_key}",
            "Content-Type": "application/json"
        }
        
        # 设置重排序支持
        self.supports_reranking = (
            self.config.supports_reranking and 
            self.config.rerank_model is not None
        )
        
        logger.info(f"SiliconFlow provider initialized. Reranking: {self.supports_reranking}")

    def _call_embedding_api(self, texts: List[str]) -> List[List[float]]:
        """调用SiliconFlow embedding API"""
        payload = {
            "model": self.config.base_model,
            "input": texts,
            "encoding_format": "float"
        }
        
        try:
            response = requests.post(
                f"{self.config.api_base}/embeddings",
                json=payload,
                headers=self.headers,
                timeout=30
            )
            response.raise_for_status()
            
            result = response.json()
            embeddings = [item["embedding"] for item in result["data"]]
            return embeddings
            
        except requests.RequestException as e:
            logger.error(f"SiliconFlow embedding API error: {str(e)}")
            raise

    async def _call_embedding_api_async(self, texts: List[str]) -> List[List[float]]:
        """异步调用SiliconFlow embedding API"""
        payload = {
            "model": self.config.base_model,
            "input": texts,
            "encoding_format": "float"
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.config.api_base}/embeddings",
                    json=payload,
                    headers=self.headers,
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    response.raise_for_status()
                    result = await response.json()
                    embeddings = [item["embedding"] for item in result["data"]]
                    return embeddings
                    
        except Exception as e:
            logger.error(f"SiliconFlow async embedding API error: {str(e)}")
            raise

    def _call_rerank_api(self, query: str, documents: List[str]) -> List[Dict[str, Any]]:
        """调用SiliconFlow rerank API"""
        if not self.supports_reranking:
            return []
            
        payload = {
            "model": self.config.rerank_model,
            "query": query,
            "documents": documents,
            "top_k": len(documents),
            "return_documents": False
        }
        
        try:
            response = requests.post(
                f"{self.config.api_base}/rerank",
                json=payload,
                headers=self.headers,
                timeout=30
            )
            response.raise_for_status()
            
            result = response.json()
            return result.get("results", [])
            
        except requests.RequestException as e:
            logger.error(f"SiliconFlow rerank API error: {str(e)}")
            raise

    async def _call_rerank_api_async(self, query: str, documents: List[str]) -> List[Dict[str, Any]]:
        """异步调用SiliconFlow rerank API"""
        if not self.supports_reranking:
            return []
            
        payload = {
            "model": self.config.rerank_model,
            "query": query,
            "documents": documents,
            "top_k": len(documents),
            "return_documents": False
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.config.api_base}/rerank",
                    json=payload,
                    headers=self.headers,
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    response.raise_for_status()
                    result = await response.json()
                    return result.get("results", [])
                    
        except Exception as e:
            logger.error(f"SiliconFlow async rerank API error: {str(e)}")
            raise

    # Embedding methods
    def get_embedding(self, text: str, stage: EmbeddingProvider.Step = EmbeddingProvider.Step.BASE, **kwargs) -> List[float]:
        return self._call_embedding_api([text])[0]

    async def async_get_embedding(self, text: str, stage: EmbeddingProvider.Step = EmbeddingProvider.Step.BASE, **kwargs) -> List[float]:
        result = await self._call_embedding_api_async([text])
        return result[0]

    def get_embeddings(self, texts: List[str], stage: EmbeddingProvider.Step = EmbeddingProvider.Step.BASE, **kwargs) -> List[List[float]]:
        return self._call_embedding_api(texts)

    async def async_get_embeddings(self, texts: List[str], stage: EmbeddingProvider.Step = EmbeddingProvider.Step.BASE, **kwargs) -> List[List[float]]:
        return await self._call_embedding_api_async(texts)

    # Reranking methods
    def rerank(
        self,
        query: str,
        results: List[ChunkSearchResult],
        stage: EmbeddingProvider.Step = EmbeddingProvider.Step.RERANK,
        limit: int = 10,
    ) -> List[ChunkSearchResult]:
        if not self.supports_reranking:
            logger.warning("Reranking not supported, returning original results")
            return results[:limit]

        texts = [result.text for result in results]
        
        try:
            rerank_results = self._call_rerank_api(query, texts)
            
            # 处理重排序结果
            scored_results = []
            for rank_info in rerank_results:
                original_result = results[rank_info["index"]]
                copied_result = copy(original_result)
                copied_result.score = rank_info["relevance_score"]
                scored_results.append(copied_result)

            # 按分数排序并返回限制数量的结果
            scored_results.sort(key=lambda x: x.score, reverse=True)
            logger.info(f"Reranked {len(results)} results, returning top {min(limit, len(scored_results))}")
            return scored_results[:limit]
            
        except Exception as e:
            logger.error(f"Reranking failed: {str(e)}, returning original results")
            return results[:limit]

    async def arerank(
        self,
        query: str,
        results: List[ChunkSearchResult],
        stage: EmbeddingProvider.Step = EmbeddingProvider.Step.RERANK,
        limit: int = 10,
    ) -> List[ChunkSearchResult]:
        if not self.supports_reranking:
            logger.warning("Reranking not supported, returning original results")
            return results[:limit]

        texts = [result.text for result in results]
        
        try:
            rerank_results = await self._call_rerank_api_async(query, texts)
            
            # 处理重排序结果
            scored_results = []
            for rank_info in rerank_results:
                original_result = results[rank_info["index"]]
                copied_result = copy(original_result)
                copied_result.score = rank_info["relevance_score"]
                scored_results.append(copied_result)

            # 按分数排序并返回限制数量的结果
            scored_results.sort(key=lambda x: x.score, reverse=True)
            logger.info(f"Async reranked {len(results)} results, returning top {min(limit, len(scored_results))}")
            return scored_results[:limit]
            
        except Exception as e:
            logger.error(f"Async reranking failed: {str(e)}, returning original results")
            return results[:limit]
