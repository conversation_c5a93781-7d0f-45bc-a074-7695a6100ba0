@echo off
echo ========================================
echo R2R Simple Deployment Script
echo ========================================

echo.
echo Checking configuration files...
if not exist "user_configs\r2r_siliconflow_simple.toml" (
    echo ERROR: Configuration file user_configs\r2r_siliconflow_simple.toml not found
    pause
    exit /b 1
)

echo.
echo Checking environment file...
if not exist "env\r2r.env" (
    echo ERROR: Environment file env\r2r.env not found
    pause
    exit /b 1
)

echo.
echo REMINDER: Make sure you have set SILICONFLOW_API_KEY in env\r2r.env
echo.

echo Stopping existing containers...
docker-compose down

echo.
echo Cleaning up old containers and images...
docker system prune -f

echo.
echo Pulling latest images...
docker-compose pull

echo.
echo Starting services in order...
echo 1. Starting database service...
docker-compose --profile postgres up -d postgres

echo Waiting for database to start...
timeout /t 15 /nobreak > nul

echo 2. Starting MinIO storage service...
docker-compose --profile minio up -d minio

echo Waiting for MinIO to start...
timeout /t 10 /nobreak > nul

echo 3. Starting SiliconFlow reranking proxy...
docker-compose up -d siliconflow-proxy

echo Waiting for proxy service to start...
timeout /t 10 /nobreak > nul

echo 4. Starting graph clustering service...
docker-compose up -d graph_clustering

echo Waiting for graph clustering service to start...
timeout /t 10 /nobreak > nul

echo 5. Starting R2R core service...
docker-compose up -d r2r

echo Waiting for R2R service to start...
timeout /t 15 /nobreak > nul

echo 6. Starting R2R Dashboard...
docker-compose up -d r2r-dashboard

echo.
echo ========================================
echo Deployment Complete!
echo ========================================
echo.
echo Service URLs:
echo - R2R API: http://localhost:7272
echo - R2R Dashboard: http://localhost:7273
echo - SiliconFlow Proxy: http://localhost:8080
echo - PostgreSQL: localhost:5432
echo - MinIO: http://localhost:9001
echo.
echo Default login credentials:
echo - Email: <EMAIL>
echo - Password: change_me_immediately
echo.
echo Checking service status...
docker-compose ps

echo.
echo If you encounter issues, check logs with:
echo docker-compose logs r2r
echo docker-compose logs r2r-dashboard
echo.
pause
